#!/usr/bin/env python3
"""
Basic API Test Script

Simple test script to verify the FastAPI endpoints are working correctly.
This script tests the basic functionality without requiring a full test suite.
"""

import requests
import time
import json
from pathlib import Path

# API configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_DOCUMENT_CONTENT = """
Natural Language Processing (NLP) is a field of artificial intelligence that focuses on the interaction between computers and humans through natural language. The ultimate objective of NLP is to read, decipher, understand, and make sense of human language in a valuable way.

Key components of NLP include:
1. Tokenization - Breaking text into individual words or tokens
2. Part-of-speech tagging - Identifying grammatical roles of words
3. Named entity recognition - Identifying proper nouns and entities
4. Sentiment analysis - Determining emotional tone of text
5. Machine translation - Converting text from one language to another

NLP applications are everywhere in modern technology, from search engines to chatbots, from voice assistants to automated translation services.
"""

def test_health_endpoint():
    """Test the health check endpoint"""
    print("Testing health endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        print(f"Health check status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Service status: {data.get('status')}")
            return True
        else:
            print(f"Health check failed: {response.text}")
            return False
    except Exception as e:
        print(f"Health check error: {e}")
        return False

def test_status_endpoint():
    """Test the service status endpoint"""
    print("\nTesting status endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/status")
        print(f"Status check: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Initialized: {data.get('initialized')}")
            print(f"Loaded documents: {data.get('loaded_documents', 0)}")
            print(f"Total chunks: {data.get('total_chunks', 0)}")
            return True
        else:
            print(f"Status check failed: {response.text}")
            return False
    except Exception as e:
        print(f"Status check error: {e}")
        return False

def test_ingest_endpoint():
    """Test document ingestion endpoint"""
    print("\nTesting ingest endpoint...")
    try:
        # Create a temporary test file
        test_file_path = Path("test_document.txt")
        with open(test_file_path, "w") as f:
            f.write(TEST_DOCUMENT_CONTENT)
        
        # Upload the file
        with open(test_file_path, "rb") as f:
            files = {"file": ("test_document.txt", f, "text/plain")}
            response = requests.post(f"{API_BASE_URL}/ingest", files=files)
        
        print(f"Ingest status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Filename: {data.get('filename')}")
            print(f"Chunks added: {data.get('chunks_added')}")
            print(f"Processing time: {data.get('processing_time'):.2f}s")
            print(f"Total chunks: {data.get('total_chunks')}")
            
            # Clean up
            test_file_path.unlink()
            return data.get('success', False)
        else:
            print(f"Ingest failed: {response.text}")
            # Clean up
            if test_file_path.exists():
                test_file_path.unlink()
            return False
            
    except Exception as e:
        print(f"Ingest error: {e}")
        # Clean up
        test_file_path = Path("test_document.txt")
        if test_file_path.exists():
            test_file_path.unlink()
        return False

def test_query_endpoint():
    """Test document querying endpoint"""
    print("\nTesting query endpoint...")
    try:
        query_data = {
            "question": "What is Natural Language Processing?",
            "include_sources": True
        }
        
        response = requests.post(
            f"{API_BASE_URL}/query",
            json=query_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Query status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Question: {data.get('question')}")
            print(f"Answer: {data.get('answer')[:200]}...")
            print(f"Confidence: {data.get('confidence_score'):.2f}")
            print(f"Response time: {data.get('response_time'):.2f}s")
            print(f"Source documents: {data.get('source_documents')}")
            print(f"Retrieved contexts: {len(data.get('retrieved_contexts', []))}")
            return True
        else:
            print(f"Query failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"Query error: {e}")
        return False

def main():
    """Run all API tests"""
    print("=" * 60)
    print("RAG API Basic Test Suite")
    print("=" * 60)
    
    # Wait a moment for API to be ready
    print("Waiting for API to be ready...")
    time.sleep(2)
    
    # Run tests
    tests = [
        ("Health Check", test_health_endpoint),
        ("Status Check", test_status_endpoint),
        ("Document Ingestion", test_ingest_endpoint),
        ("Document Query", test_query_endpoint),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        success = test_func()
        results.append((test_name, success))
        
        if success:
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! API is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the API implementation.")
        return False

if __name__ == "__main__":
    main()
