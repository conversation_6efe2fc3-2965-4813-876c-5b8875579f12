services:
  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    container_name: open-webui
    ports:
      - "3000:8080" # Map container port 8080 to host port 3000
    environment:
      # Ollama configuration (host network for Ollama)
      - OLLAMA_BASE_URL=http://**************:11434

      # OpenAI API configuration for RAG service (host network)
      - OPENAI_API_BASE_URL=http://**************:8000/v1
      - OPENAI_API_KEY=dummy-key

      # Open WebUI configuration
      - WEBUI_NAME=RAG Assistant
      - WEBUI_URL=http://localhost:3000
      - ENABLE_SIGNUP=true
      - DEFAULT_USER_ROLE=admin
      - ENABLE_ADMIN_EXPORT=true

      # Optional: Enable additional features
      - ENABLE_RAG_HYBRID_SEARCH=true
      - ENABLE_RAG_WEB_LOADER_SSL_VERIFICATION=false

    volumes:
      # Persist Open WebUI data
      - open-webui-data:/app/backend/data

    extra_hosts:
      # Allow container to access host services (Ollama)
      - "host.docker.internal:host-gateway"

    restart: unless-stopped

    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  open-webui-data:
    driver: local
