#!/usr/bin/env python3
"""
Test script for Open WebUI integration with RAG service.

This script tests the end-to-end integration between Open WebUI and our RAG service
using OpenAI-compatible API endpoints.
"""

import requests
import json
import time
import sys

# Configuration
FASTAPI_BASE_URL = "http://localhost:8000"
OPENWEBUI_BASE_URL = "http://localhost:3000"
RAG_API_BASE_URL = "http://localhost:8000/v1"

def test_fastapi_health():
    """Test FastAPI service health."""
    print("🔍 Testing FastAPI service health...")
    try:
        response = requests.get(f"{FASTAPI_BASE_URL}/api/v1/health", timeout=5)
        if response.status_code == 200:
            print("✅ FastAPI service is healthy")
            return True
        else:
            print(f"❌ FastAPI health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ FastAPI health check failed: {e}")
        return False

def test_openwebui_health():
    """Test Open WebUI health."""
    print("🔍 Testing Open WebUI health...")
    try:
        response = requests.get(f"{OPENWEBUI_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Open WebUI is healthy")
            return True
        else:
            print(f"❌ Open WebUI health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Open WebUI health check failed: {e}")
        return False

def test_openai_models_endpoint():
    """Test OpenAI-compatible models endpoint."""
    print("🔍 Testing OpenAI-compatible /v1/models endpoint...")
    try:
        response = requests.get(f"{RAG_API_BASE_URL}/models", timeout=5)
        if response.status_code == 200:
            data = response.json()
            models = [model['id'] for model in data.get('data', [])]
            print(f"✅ Models endpoint working. Available models: {models}")
            return 'rag-assistant' in models
        else:
            print(f"❌ Models endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Models endpoint failed: {e}")
        return False

def test_openai_chat_completions():
    """Test OpenAI-compatible chat completions endpoint."""
    print("🔍 Testing OpenAI-compatible /v1/chat/completions endpoint...")
    try:
        payload = {
            "model": "rag-assistant",
            "messages": [
                {"role": "user", "content": "What is this document about?"}
            ],
            "temperature": 0.1
        }
        
        response = requests.post(
            f"{RAG_API_BASE_URL}/chat/completions",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            content = data['choices'][0]['message']['content']
            print("✅ Chat completions endpoint working")
            print(f"📝 Response preview: {content[:100]}...")
            
            # Check for source citations
            if "Sources:" in content or "Source:" in content:
                print("✅ Source citations found in response")
                return True
            else:
                print("⚠️  No source citations found in response")
                return True  # Still consider it working
        else:
            print(f"❌ Chat completions failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Chat completions failed: {e}")
        return False

def test_document_ingestion():
    """Test document ingestion through API."""
    print("🔍 Testing document ingestion...")
    try:
        # Check if test document exists
        import os
        if not os.path.exists("test_document.txt"):
            print("⚠️  test_document.txt not found, skipping ingestion test")
            return True
            
        with open("test_document.txt", "rb") as f:
            files = {"file": ("test_document.txt", f, "text/plain")}
            response = requests.post(
                f"{FASTAPI_BASE_URL}/api/v1/ingest",
                files=files,
                timeout=30
            )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Document ingestion successful: {data.get('chunks_added', 0)} chunks added")
                return True
            else:
                print(f"❌ Document ingestion failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ Document ingestion failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Document ingestion failed: {e}")
        return False

def test_service_status():
    """Test service status endpoint."""
    print("🔍 Testing service status...")
    try:
        response = requests.get(f"{FASTAPI_BASE_URL}/api/v1/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Service status: {data.get('status', 'unknown')}")
            print(f"📊 Total documents: {data.get('pipeline_stats', {}).get('vector_store', {}).get('total_documents', 0)}")
            return True
        else:
            print(f"❌ Service status failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Service status failed: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🚀 Starting Open WebUI + RAG Service Integration Tests")
    print("=" * 60)
    
    tests = [
        ("FastAPI Health", test_fastapi_health),
        ("Open WebUI Health", test_openwebui_health),
        ("Service Status", test_service_status),
        ("Document Ingestion", test_document_ingestion),
        ("OpenAI Models Endpoint", test_openai_models_endpoint),
        ("OpenAI Chat Completions", test_openai_chat_completions),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Open WebUI integration is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
