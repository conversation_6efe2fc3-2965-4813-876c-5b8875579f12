<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RAG System - Document Management</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background-color: #f5f5f5;
        color: #333;
        line-height: 1.6;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }

      .header h1 {
        color: #2c3e50;
        margin-bottom: 10px;
      }

      .header p {
        color: #7f8c8d;
      }

      .section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }

      .section h2 {
        color: #2c3e50;
        margin-bottom: 15px;
        border-bottom: 2px solid #3498db;
        padding-bottom: 5px;
      }

      .upload-area {
        border: 2px dashed #bdc3c7;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        transition: border-color 0.3s;
        margin-bottom: 20px;
      }

      .upload-area:hover {
        border-color: #3498db;
      }

      .upload-area.dragover {
        border-color: #2ecc71;
        background-color: #ecf0f1;
      }

      .file-input {
        display: none;
      }

      .upload-btn {
        background: #3498db;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.3s;
      }

      .upload-btn:hover {
        background: #2980b9;
      }

      .upload-btn:disabled {
        background: #bdc3c7;
        cursor: not-allowed;
      }

      .progress-bar {
        width: 100%;
        height: 20px;
        background: #ecf0f1;
        border-radius: 10px;
        overflow: hidden;
        margin: 10px 0;
        display: none;
      }

      .progress-fill {
        height: 100%;
        background: #2ecc71;
        width: 0%;
        transition: width 0.3s;
      }

      .table-container {
        overflow-x: auto;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
      }

      th,
      td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #ecf0f1;
      }

      th {
        background: #f8f9fa;
        font-weight: 600;
        color: #2c3e50;
      }

      tr:hover {
        background: #f8f9fa;
      }

      .btn {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
      }

      .btn-danger {
        background: #e74c3c;
        color: white;
      }

      .btn-danger:hover {
        background: #c0392b;
      }

      .btn-info {
        background: #3498db;
        color: white;
      }

      .btn-info:hover {
        background: #2980b9;
      }

      .btn-secondary {
        background: #95a5a6;
        color: white;
      }

      .btn-secondary:hover {
        background: #7f8c8d;
      }

      .status-message {
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
        display: none;
      }

      .status-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .status-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .status-info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }

      .loading {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
      }

      .stat-card {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
      }

      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #3498db;
      }

      .stat-label {
        color: #7f8c8d;
        font-size: 14px;
      }

      .empty-state {
        text-align: center;
        padding: 40px;
        color: #7f8c8d;
      }

      .empty-state i {
        font-size: 48px;
        margin-bottom: 15px;
        display: block;
      }

      .actions {
        display: flex;
        gap: 10px;
        margin-top: 20px;
      }

      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
      }

      .modal-content {
        background-color: white;
        margin: 15% auto;
        padding: 20px;
        border-radius: 8px;
        width: 80%;
        max-width: 500px;
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }

      .close {
        color: #aaa;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
      }

      .close:hover {
        color: #000;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>RAG System - Document Management</h1>
        <p>
          Manage your knowledge base documents. Upload new documents, view
          existing ones, and delete documents as needed.
        </p>
      </div>

      <div id="statusMessage" class="status-message"></div>

      <div class="stats" id="statsContainer">
        <div class="stat-card">
          <div class="stat-number" id="totalDocuments">-</div>
          <div class="stat-label">Total Documents</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="totalChunks">-</div>
          <div class="stat-label">Total Chunks</div>
        </div>
      </div>

      <div class="section">
        <h2>Upload New Document</h2>
        <div class="upload-area" id="uploadArea">
          <p>Drag and drop files here or click to select</p>
          <p style="color: #7f8c8d; font-size: 14px; margin-top: 10px">
            Supported formats: .txt, .md, .pdf, .docx, .pptx, .xlsx, .html, .xml, .json, .csv, .yml, .yaml
          </p>
          <input
            type="file"
            id="fileInput"
            class="file-input"
            accept=".txt,.md"
            multiple
          />
          <button class="upload-btn" id="uploadBtn">Choose Files</button>
        </div>
        <div class="progress-bar" id="progressBar">
          <div class="progress-fill" id="progressFill"></div>
        </div>
      </div>

      <div class="section">
        <h2>Document Library</h2>
        <div class="actions">
          <button class="btn btn-info" id="refreshBtn">
            <span id="refreshIcon">🔄</span> Refresh
          </button>
          <button class="btn btn-secondary" id="rebuildBtn">
            🔧 Rebuild Index
          </button>
        </div>

        <div class="table-container">
          <table id="documentsTable">
            <thead>
              <tr>
                <th>Filename</th>
                <th>File Size</th>
                <th>Chunks</th>
                <th>Upload Date</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="documentsTableBody">
              <!-- Documents will be loaded here -->
            </tbody>
          </table>
        </div>

        <div id="emptyState" class="empty-state" style="display: none">
          <i>📄</i>
          <h3>No documents found</h3>
          <p>Upload your first document to get started!</p>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Confirm Deletion</h3>
          <span class="close" id="closeModal">&times;</span>
        </div>
        <p>
          Are you sure you want to delete "<span id="deleteFileName"></span>"?
        </p>
        <p style="color: #e74c3c; font-size: 14px">
          This action cannot be undone.
        </p>
        <div style="margin-top: 20px; text-align: right">
          <button class="btn btn-secondary" id="cancelDelete">Cancel</button>
          <button
            class="btn btn-danger"
            id="confirmDelete"
            style="margin-left: 10px"
          >
            Delete
          </button>
        </div>
      </div>
    </div>

    <script>
      // API base URL
      const API_BASE = "/api/v1";

      // DOM elements
      const uploadArea = document.getElementById("uploadArea");
      const fileInput = document.getElementById("fileInput");
      const uploadBtn = document.getElementById("uploadBtn");
      const progressBar = document.getElementById("progressBar");
      const progressFill = document.getElementById("progressFill");
      const statusMessage = document.getElementById("statusMessage");
      const documentsTableBody = document.getElementById("documentsTableBody");
      const emptyState = document.getElementById("emptyState");
      const refreshBtn = document.getElementById("refreshBtn");
      const refreshIcon = document.getElementById("refreshIcon");
      const totalDocuments = document.getElementById("totalDocuments");
      const totalChunks = document.getElementById("totalChunks");
      const deleteModal = document.getElementById("deleteModal");
      const deleteFileName = document.getElementById("deleteFileName");
      const confirmDelete = document.getElementById("confirmDelete");
      const cancelDelete = document.getElementById("cancelDelete");
      const closeModal = document.getElementById("closeModal");

      let currentDeleteId = null;

      // Initialize the application
      document.addEventListener("DOMContentLoaded", function () {
        loadDocuments();
        setupEventListeners();
      });

      function setupEventListeners() {
        // File upload events
        uploadBtn.addEventListener("click", () => fileInput.click());
        fileInput.addEventListener("change", handleFileSelect);

        // Drag and drop events
        uploadArea.addEventListener("dragover", handleDragOver);
        uploadArea.addEventListener("dragleave", handleDragLeave);
        uploadArea.addEventListener("drop", handleDrop);

        // Refresh button
        refreshBtn.addEventListener("click", loadDocuments);

        // Rebuild index button
        rebuildBtn.addEventListener("click", startRebuildIndex);

        // Modal events
        closeModal.addEventListener("click", closeDeleteModal);
        cancelDelete.addEventListener("click", closeDeleteModal);
        confirmDelete.addEventListener("click", handleConfirmDelete);

        // Close modal when clicking outside
        window.addEventListener("click", function (event) {
          if (event.target === deleteModal) {
            closeDeleteModal();
          }
        });
      }

      function handleDragOver(e) {
        e.preventDefault();
        uploadArea.classList.add("dragover");
      }

      function handleDragLeave(e) {
        e.preventDefault();
        uploadArea.classList.remove("dragover");
      }

      function handleDrop(e) {
        e.preventDefault();
        uploadArea.classList.remove("dragover");
        const files = e.dataTransfer.files;
        handleFiles(files);
      }

      function handleFileSelect(e) {
        const files = e.target.files;
        handleFiles(files);
      }

      function handleFiles(files) {
        if (files.length === 0) return;

        // For now, handle one file at a time
        const file = files[0];
        uploadFile(file);
      }

      async function uploadFile(file) {
        const formData = new FormData();
        formData.append("file", file);

        try {
          showProgress(true);
          showStatus("Uploading file...", "info");

          const response = await fetch(`${API_BASE}/ingest`, {
            method: "POST",
            body: formData,
          });

          const result = await response.json();

          if (response.ok) {
            showStatus(
              `Successfully uploaded "${file.name}" - ${result.chunks_added} chunks created`,
              "success"
            );
            loadDocuments(); // Refresh the document list
          } else {
            showStatus(
              `Upload failed: ${result.detail || result.message}`,
              "error"
            );
          }
        } catch (error) {
          showStatus(`Upload error: ${error.message}`, "error");
        } finally {
          showProgress(false);
          fileInput.value = ""; // Clear the input
        }
      }

      async function loadDocuments() {
        try {
          setRefreshLoading(true);

          const response = await fetch(`${API_BASE}/documents`);
          const result = await response.json();

          if (response.ok) {
            displayDocuments(result.documents);
            updateStats(result.total_documents, result.total_chunks);
          } else {
            showStatus(
              `Failed to load documents: ${result.detail || result.message}`,
              "error"
            );
          }
        } catch (error) {
          showStatus(`Error loading documents: ${error.message}`, "error");
        } finally {
          setRefreshLoading(false);
        }
      }

      function displayDocuments(documents) {
        documentsTableBody.innerHTML = "";

        if (documents.length === 0) {
          emptyState.style.display = "block";
          document.getElementById("documentsTable").style.display = "none";
          return;
        }

        emptyState.style.display = "none";
        document.getElementById("documentsTable").style.display = "table";

        documents.forEach((doc) => {
          const row = document.createElement("tr");
          row.innerHTML = `
                    <td>${escapeHtml(doc.filename)}</td>
                    <td>${formatFileSize(doc.file_size)}</td>
                    <td>${doc.chunk_count}</td>
                    <td>${formatDate(doc.upload_date)}</td>
                    <td>
                        <button class="btn btn-info" onclick="viewDocument('${
                          doc.document_id
                        }')">View</button>
                        <button class="btn btn-danger" onclick="deleteDocument('${
                          doc.document_id
                        }', '${escapeHtml(doc.filename)}')">Delete</button>
                    </td>
                `;
          documentsTableBody.appendChild(row);
        });
      }

      function updateStats(docCount, chunkCount) {
        totalDocuments.textContent = docCount;
        totalChunks.textContent = chunkCount;
      }

      function deleteDocument(documentId, filename) {
        currentDeleteId = documentId;
        deleteFileName.textContent = filename;
        deleteModal.style.display = "block";
      }

      async function handleConfirmDelete() {
        if (!currentDeleteId) return;

        try {
          const response = await fetch(
            `${API_BASE}/documents/${currentDeleteId}`,
            {
              method: "DELETE",
            }
          );

          const result = await response.json();

          if (response.ok) {
            showStatus(
              `Successfully deleted "${result.filename}" - ${result.chunks_deleted} chunks removed`,
              "success"
            );
            loadDocuments(); // Refresh the document list
          } else {
            showStatus(
              `Delete failed: ${result.detail || result.message}`,
              "error"
            );
          }
        } catch (error) {
          showStatus(`Delete error: ${error.message}`, "error");
        } finally {
          closeDeleteModal();
        }
      }

      function closeDeleteModal() {
        deleteModal.style.display = "none";
        currentDeleteId = null;
      }

      async function viewDocument(documentId) {
        try {
          const response = await fetch(
            `${API_BASE}/documents/${documentId}/metadata`
          );
          const result = await response.json();

          if (response.ok) {
            // For now, just show an alert with document info
            // In the future, this could open a detailed view modal
            alert(
              `Document: ${result.document.filename}\nChunks: ${
                result.document.chunk_count
              }\nFile Size: ${formatFileSize(result.document.file_size)}`
            );
          } else {
            showStatus(
              `Failed to load document details: ${
                result.detail || result.message
              }`,
              "error"
            );
          }
        } catch (error) {
          showStatus(
            `Error loading document details: ${error.message}`,
            "error"
          );
        }
      }

      function showStatus(message, type) {
        statusMessage.textContent = message;
        statusMessage.className = `status-message status-${type}`;
        statusMessage.style.display = "block";

        // Auto-hide after 5 seconds
        setTimeout(() => {
          statusMessage.style.display = "none";
        }, 5000);
      }

      function showProgress(show) {
        progressBar.style.display = show ? "block" : "none";
        if (show) {
          progressFill.style.width = "100%";
        } else {
          progressFill.style.width = "0%";
        }
      }

      function setRefreshLoading(loading) {
        refreshBtn.disabled = loading;
        refreshIcon.textContent = loading ? "⏳" : "🔄";
      }

      // Rebuild index functionality
      async function startRebuildIndex() {
        if (
          !confirm(
            "Are you sure you want to rebuild the index? This will clear all existing documents and re-index from the golden dataset."
          )
        ) {
          return;
        }

        const rebuildBtn = document.getElementById("rebuildBtn");
        const originalText = rebuildBtn.innerHTML;

        try {
          // Disable button and show loading state
          rebuildBtn.disabled = true;
          rebuildBtn.innerHTML = "⏳ Starting rebuild...";

          // Start the rebuild operation
          const response = await fetch("/api/v1/rebuild-index", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              source_directory: null,
              backup_existing: true,
            }),
          });

          const result = await response.json();

          if (result.success) {
            // Start polling for progress
            pollRebuildProgress(result.operation_id, rebuildBtn, originalText);
          } else {
            throw new Error(
              result.error || "Failed to start rebuild operation"
            );
          }
        } catch (error) {
          console.error("Error starting rebuild:", error);
          showMessage("Error starting rebuild: " + error.message, "error");
          rebuildBtn.disabled = false;
          rebuildBtn.innerHTML = originalText;
        }
      }

      async function pollRebuildProgress(operationId, button, originalText) {
        try {
          const response = await fetch(
            `/api/v1/rebuild-index/status/${operationId}`
          );
          const status = await response.json();

          // Update button text with progress
          button.innerHTML = `⏳ ${
            status.current_step
          } (${status.progress.toFixed(1)}%)`;

          if (status.status === "completed") {
            showMessage(
              `Index rebuild completed! Processed ${status.documents_processed} documents, created ${status.chunks_created} chunks.`,
              "success"
            );
            button.disabled = false;
            button.innerHTML = originalText;
            // Refresh the documents list
            loadDocuments();
          } else if (status.status === "failed") {
            throw new Error(status.error_message || "Rebuild operation failed");
          } else if (status.status === "running") {
            // Continue polling every 2 seconds
            setTimeout(
              () => pollRebuildProgress(operationId, button, originalText),
              2000
            );
          }
        } catch (error) {
          console.error("Error polling rebuild status:", error);
          showMessage("Error during rebuild: " + error.message, "error");
          button.disabled = false;
          button.innerHTML = originalText;
        }
      }

      function formatFileSize(bytes) {
        if (bytes === 0) return "0 Bytes";
        const k = 1024;
        const sizes = ["Bytes", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
      }

      function formatDate(dateString) {
        try {
          const date = new Date(dateString);
          return date.toLocaleDateString() + " " + date.toLocaleTimeString();
        } catch {
          return "Unknown";
        }
      }

      function escapeHtml(text) {
        const div = document.createElement("div");
        div.textContent = text;
        return div.innerHTML;
      }
    </script>
  </body>
</html>
