#!/usr/bin/env python3
"""
RAG CLI - Command Line Interface for RAG Pipeline

This script provides a command-line interface for the RAG system,
allowing users to process documents and ask questions.

Usage:
    python rag_cli.py document.txt "What is the main topic?"
    python rag_cli.py --help
"""

import argparse
import sys
import time
from pathlib import Path
from typing import List, Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.rag.pipeline import RAGPipeline, create_rag_pipeline


def setup_argument_parser() -> argparse.ArgumentParser:
    """Set up command line argument parser"""
    parser = argparse.ArgumentParser(
        description="RAG CLI - Query documents using Retrieval-Augmented Generation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python rag_cli.py document.txt "What is machine learning?"
  python rag_cli.py doc1.txt doc2.txt "Compare the concepts"
  python rag_cli.py --chunk-size 1000 document.txt "Summarize this"
  python rag_cli.py --top-k 5 --temperature 0.2 doc.txt "Explain in detail"
        """
    )
    
    # Required arguments
    parser.add_argument(
        'documents',
        nargs='+',
        help='Path(s) to document file(s) to process (.txt files only)'
    )
    
    parser.add_argument(
        'question',
        help='Question to ask about the documents'
    )
    
    # Optional configuration arguments
    parser.add_argument(
        '--chunk-size',
        type=int,
        default=500,
        help='Size of document chunks in characters (default: 500)'
    )
    
    parser.add_argument(
        '--chunk-overlap',
        type=int,
        default=50,
        help='Overlap between chunks in characters (default: 50)'
    )
    
    parser.add_argument(
        '--top-k',
        type=int,
        default=3,
        help='Number of relevant chunks to retrieve (default: 3)'
    )
    
    parser.add_argument(
        '--similarity-threshold',
        type=float,
        default=0.0,
        help='Minimum similarity score for retrieval (default: 0.0)'
    )
    
    parser.add_argument(
        '--temperature',
        type=float,
        default=0.1,
        help='LLM temperature for response generation (default: 0.1)'
    )
    
    parser.add_argument(
        '--embedding-model',
        default='all-MiniLM-L6-v2',
        help='Sentence transformer model for embeddings (default: all-MiniLM-L6-v2)'
    )
    
    parser.add_argument(
        '--llm-model',
        default='qwen3:8b',
        help='Ollama model for response generation (default: qwen3:8b)'
    )
    
    parser.add_argument(
        '--verbose',
        '-v',
        action='store_true',
        help='Enable verbose output'
    )
    
    parser.add_argument(
        '--show-sources',
        action='store_true',
        help='Show detailed source information'
    )
    
    parser.add_argument(
        '--show-stats',
        action='store_true',
        help='Show pipeline statistics'
    )
    
    return parser


def validate_documents(document_paths: List[str]) -> List[str]:
    """
    Validate that document files exist and are supported.
    
    Args:
        document_paths: List of document file paths
        
    Returns:
        List of valid document paths
        
    Raises:
        SystemExit: If no valid documents found
    """
    valid_documents = []
    errors = []
    
    for doc_path in document_paths:
        path = Path(doc_path)
        
        if not path.exists():
            errors.append(f"File not found: {doc_path}")
            continue
        
        if not path.is_file():
            errors.append(f"Not a file: {doc_path}")
            continue
        
        if path.suffix.lower() != '.txt':
            errors.append(f"Unsupported file type: {doc_path} (only .txt files supported)")
            continue
        
        valid_documents.append(str(path))
    
    # Report errors
    if errors:
        print("Document validation errors:", file=sys.stderr)
        for error in errors:
            print(f"  - {error}", file=sys.stderr)
    
    if not valid_documents:
        print("Error: No valid documents found to process", file=sys.stderr)
        sys.exit(1)
    
    return valid_documents


def print_response(response, show_sources: bool = False, verbose: bool = False):
    """
    Print the RAG pipeline response in a formatted way.
    
    Args:
        response: RAGPipelineResponse object
        show_sources: Whether to show detailed source information
        verbose: Whether to show verbose output
    """
    print("=" * 60)
    print("RAG SYSTEM RESPONSE")
    print("=" * 60)
    
    print(f"\nQuestion: {response.question}")
    print(f"\nAnswer:\n{response.answer}")
    
    # Basic metadata
    print(f"\nConfidence Score: {response.confidence_score:.2f}")
    print(f"Response Time: {response.response_time:.2f} seconds")
    
    # Source documents
    if response.source_documents:
        print(f"\nSource Documents: {', '.join(response.source_documents)}")
    else:
        print("\nSource Documents: None")
    
    # Detailed source information
    if show_sources and response.retrieved_contexts:
        print(f"\nRetrieved Contexts ({len(response.retrieved_contexts)}):")
        for i, context in enumerate(response.retrieved_contexts, 1):
            print(f"\n  {i}. Similarity: {context.similarity_score:.3f}")
            print(f"     Source: {context.source_document}")
            print(f"     Content: {context.content[:200]}...")
    
    # Verbose information
    if verbose:
        print(f"\nPipeline Metadata:")
        for key, value in response.pipeline_metadata.items():
            print(f"  {key}: {value}")


def main():
    """Main CLI function"""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.INFO)
    
    try:
        print("Initializing RAG Pipeline...")
        start_time = time.time()
        
        # Validate documents
        valid_documents = validate_documents(args.documents)
        print(f"Found {len(valid_documents)} valid document(s)")
        
        # Create RAG pipeline with configuration
        pipeline = create_rag_pipeline(
            chunk_size=args.chunk_size,
            chunk_overlap=args.chunk_overlap,
            embedding_model=args.embedding_model,
            llm_model=args.llm_model,
            top_k_retrieval=args.top_k,
            similarity_threshold=args.similarity_threshold,
            llm_temperature=args.temperature
        )
        
        init_time = time.time() - start_time
        print(f"Pipeline initialized in {init_time:.2f} seconds")
        
        # Load documents
        print("\nLoading documents...")
        load_start = time.time()
        
        total_chunks = pipeline.load_documents(valid_documents)
        
        load_time = time.time() - load_start
        print(f"Loaded {total_chunks} chunks in {load_time:.2f} seconds")
        
        # Process query
        print(f"\nProcessing query: '{args.question}'")
        response = pipeline.query(args.question)
        
        # Print response
        print_response(
            response,
            show_sources=args.show_sources,
            verbose=args.verbose
        )
        
        # Show pipeline statistics if requested
        if args.show_stats:
            print("\n" + "=" * 60)
            print("PIPELINE STATISTICS")
            print("=" * 60)
            
            stats = pipeline.get_pipeline_stats()
            for key, value in stats.items():
                if isinstance(value, dict):
                    print(f"\n{key.upper()}:")
                    for sub_key, sub_value in value.items():
                        print(f"  {sub_key}: {sub_value}")
                else:
                    print(f"{key}: {value}")
        
        print("\n" + "=" * 60)
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user", file=sys.stderr)
        sys.exit(1)
    
    except Exception as e:
        print(f"\nError: {e}", file=sys.stderr)
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
