RAG System Test Document

Introduction
This is a test document for the RAG (Retrieval-Augmented Generation) system integration with Open WebUI.

Key Features
The RAG system includes the following features:
- Document ingestion from PDF, TXT, and MD files
- Vector-based document retrieval using ChromaDB
- Question answering using Qwen3 8B model
- FastAPI backend with OpenAI-compatible endpoints
- Open WebUI frontend integration

Technical Architecture
The system consists of:
1. FastAPI Backend: Provides REST API endpoints for document management and querying
2. ChromaDB: Vector database for storing document embeddings
3. Sentence Transformers: For generating document embeddings
4. Ollama: Local LLM inference with Qwen3 8B model
5. Open WebUI: Chat interface for user interactions

Test Questions
You can ask questions like:
- What are the key features of the RAG system?
- What is the technical architecture?
- Which model is used for LLM inference?
- What database is used for vector storage?

Performance
The system is designed to:
- Process documents efficiently with configurable chunk sizes
- Maintain response times of 8-13 seconds for queries
- Provide source citations for all answers
- Support persistent storage across restarts
