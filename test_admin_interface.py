#!/usr/bin/env python3
"""
Test script for the Admin Interface functionality.

This script tests all the document management endpoints and validates
the admin interface integration.
"""

import requests
import json
import time
import tempfile
import os
from pathlib import Path

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_admin_interface_accessibility():
    """Test that the admin interface is accessible."""
    print("Testing admin interface accessibility...")
    
    response = requests.get(f"{BASE_URL}/admin")
    assert response.status_code == 200, f"Admin interface not accessible: {response.status_code}"
    assert "RAG System - Document Management" in response.text, "Admin interface content not found"
    print("✅ Admin interface is accessible")

def test_document_listing():
    """Test the document listing endpoint."""
    print("Testing document listing...")
    
    response = requests.get(f"{API_BASE}/documents")
    assert response.status_code == 200, f"Document listing failed: {response.status_code}"
    
    data = response.json()
    assert "success" in data, "Response missing success field"
    assert "documents" in data, "Response missing documents field"
    assert "total_documents" in data, "Response missing total_documents field"
    assert "total_chunks" in data, "Response missing total_chunks field"
    
    print(f"✅ Document listing works - Found {data['total_documents']} documents")
    return data

def test_document_upload():
    """Test document upload functionality."""
    print("Testing document upload...")
    
    # Create a temporary test file
    test_content = """Test Document for Admin Interface

This is a test document to validate the admin interface upload functionality.

Key Features:
- Document upload via API
- Chunk processing
- Metadata extraction
- File validation

This document should be processed into multiple chunks for testing purposes.
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        # Upload the file
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('test_admin.txt', f, 'text/plain')}
            response = requests.post(f"{API_BASE}/ingest", files=files)
        
        assert response.status_code == 200, f"Upload failed: {response.status_code}"
        
        data = response.json()
        assert data["success"], f"Upload not successful: {data}"
        assert data["chunks_added"] > 0, "No chunks were created"
        
        print(f"✅ Document upload works - Created {data['chunks_added']} chunks")
        return data["filename"]
        
    finally:
        # Clean up temporary file
        os.unlink(temp_file_path)

def test_document_metadata(document_id):
    """Test document metadata retrieval."""
    print(f"Testing document metadata for {document_id}...")
    
    response = requests.get(f"{API_BASE}/documents/{document_id}/metadata")
    assert response.status_code == 200, f"Metadata retrieval failed: {response.status_code}"
    
    data = response.json()
    assert data["success"], f"Metadata retrieval not successful: {data}"
    assert "document" in data, "Response missing document field"
    assert "chunks" in data, "Response missing chunks field"
    
    doc = data["document"]
    assert doc["document_id"] == document_id, "Document ID mismatch"
    assert doc["chunk_count"] > 0, "No chunks found"
    
    print(f"✅ Document metadata works - Found {doc['chunk_count']} chunks")

def test_document_deletion(document_id):
    """Test document deletion functionality."""
    print(f"Testing document deletion for {document_id}...")
    
    response = requests.delete(f"{API_BASE}/documents/{document_id}")
    assert response.status_code == 200, f"Deletion failed: {response.status_code}"
    
    data = response.json()
    assert data["success"], f"Deletion not successful: {data}"
    assert data["chunks_deleted"] > 0, "No chunks were deleted"
    
    print(f"✅ Document deletion works - Deleted {data['chunks_deleted']} chunks")

def test_error_handling():
    """Test error handling for invalid requests."""
    print("Testing error handling...")
    
    # Test non-existent document metadata
    response = requests.get(f"{API_BASE}/documents/nonexistent_doc/metadata")
    assert response.status_code == 404, f"Expected 404, got {response.status_code}"
    
    # Test non-existent document deletion
    response = requests.delete(f"{API_BASE}/documents/nonexistent_doc")
    assert response.status_code == 404, f"Expected 404, got {response.status_code}"
    
    print("✅ Error handling works correctly")

def test_static_files():
    """Test that static files are served correctly."""
    print("Testing static file serving...")
    
    # Test that we can access static files
    response = requests.get(f"{BASE_URL}/static/admin.html")
    assert response.status_code == 200, f"Static file not accessible: {response.status_code}"
    assert "RAG System - Document Management" in response.text, "Static file content incorrect"
    
    print("✅ Static file serving works")

def run_comprehensive_test():
    """Run all tests in sequence."""
    print("🚀 Starting comprehensive admin interface tests...\n")
    
    try:
        # Test basic accessibility
        test_admin_interface_accessibility()
        test_static_files()
        
        # Test document operations
        initial_docs = test_document_listing()
        
        # Upload a test document
        uploaded_filename = test_document_upload()
        
        # Wait a moment for processing
        time.sleep(1)
        
        # Get updated document list
        updated_docs = test_document_listing()
        assert updated_docs["total_documents"] == initial_docs["total_documents"] + 1, "Document count didn't increase"
        
        # Find the uploaded document
        uploaded_doc = None
        for doc in updated_docs["documents"]:
            if "test_admin" in doc["filename"]:
                uploaded_doc = doc
                break
        
        assert uploaded_doc is not None, "Uploaded document not found in list"
        
        # Test metadata retrieval
        test_document_metadata(uploaded_doc["document_id"])
        
        # Test deletion
        test_document_deletion(uploaded_doc["document_id"])
        
        # Verify deletion
        final_docs = test_document_listing()
        assert final_docs["total_documents"] == initial_docs["total_documents"], "Document count didn't decrease after deletion"
        
        # Test error handling
        test_error_handling()
        
        print("\n🎉 All tests passed! Admin interface is working correctly.")
        
        # Print summary
        print("\n📊 Test Summary:")
        print("- ✅ Admin interface accessibility")
        print("- ✅ Static file serving")
        print("- ✅ Document listing")
        print("- ✅ Document upload")
        print("- ✅ Document metadata retrieval")
        print("- ✅ Document deletion")
        print("- ✅ Error handling")
        print("- ✅ Integration with existing API")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    exit(0 if success else 1)
