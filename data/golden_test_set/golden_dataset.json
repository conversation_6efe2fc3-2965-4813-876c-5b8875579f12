{"schema_version": "1.0", "description": "Comprehensive golden dataset for RAG evaluation framework testing - expanded from 5 to 12 test cases covering multiple question types and edge cases", "created_date": "2025-01-27", "updated_date": "2025-01-27", "total_test_cases": 12, "coverage_analysis": {"question_categories": ["definition", "comparison", "enumeration", "concept_explanation", "factual", "multi_document", "edge_case"], "difficulty_levels": ["basic", "intermediate", "advanced"], "document_types": ["txt", "md", "mixed"]}, "test_cases": [{"id": "test_001", "question": "What is machine learning?", "ground_truth_answer": "Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed for every task.", "source_document": "ai_ml_overview.md", "expected_contexts": ["Machine learning is a subset of artificial intelligence (AI) that provides systems the ability to automatically learn and improve from experience without being explicitly programmed.", "ML focuses on the development of computer programs that can access data and use it to learn for themselves."], "difficulty": "basic", "category": "definition"}, {"id": "test_002", "question": "How does supervised learning differ from unsupervised learning?", "ground_truth_answer": "Supervised learning uses labeled training data to learn patterns, while unsupervised learning finds patterns in data without labels or predefined outcomes.", "source_document": "ai_ml_overview.md", "expected_contexts": ["Supervised learning algorithms learn from labeled training data, making predictions based on input-output pairs.", "Unsupervised learning works with unlabeled data to discover hidden patterns and structures without predefined outcomes."], "difficulty": "intermediate", "category": "comparison"}, {"id": "test_003", "question": "What are the main applications of natural language processing?", "ground_truth_answer": "The main applications of NLP include machine translation, sentiment analysis, chatbots and virtual assistants, text summarization, and information extraction from documents.", "source_document": "nlp_rag_concepts.txt", "expected_contexts": ["Natural Language Processing (NLP) has numerous practical applications including machine translation between languages, sentiment analysis of social media and reviews, and automated chatbots.", "Other key NLP applications include text summarization for news and documents, information extraction from unstructured text, and speech recognition systems."], "difficulty": "intermediate", "category": "enumeration"}, {"id": "test_004", "question": "List the key components of a typical machine learning pipeline.", "ground_truth_answer": "A typical ML pipeline includes data collection, data preprocessing and cleaning, feature engineering, model selection and training, model evaluation, and deployment with monitoring.", "source_document": "python_dev_guide.txt", "expected_contexts": ["The machine learning pipeline typically consists of several key stages: data collection from various sources, data preprocessing to clean and prepare the data.", "Following preprocessing, the pipeline includes feature engineering, model selection and training, comprehensive evaluation using appropriate metrics, and finally deployment with ongoing monitoring."], "difficulty": "intermediate", "category": "enumeration"}, {"id": "test_005", "question": "Explain the concept of overfitting in machine learning models.", "ground_truth_answer": "Overfitting occurs when a machine learning model learns the training data too well, including noise and irrelevant patterns, resulting in poor performance on new, unseen data.", "source_document": "ai_ml_overview.md", "expected_contexts": ["Overfitting happens when a model becomes too complex and learns not just the underlying patterns but also the noise in the training data.", "An overfitted model performs excellently on training data but fails to generalize to new, unseen examples."], "difficulty": "advanced", "category": "concept_explanation"}, {"id": "test_006", "question": "What is Retrieval-Augmented Generation (RAG)?", "ground_truth_answer": "RAG is a technique that combines information retrieval with text generation, allowing language models to access and use external knowledge sources to provide more accurate and up-to-date responses.", "source_document": "nlp_rag_concepts.txt", "expected_contexts": ["Retrieval-Augmented Generation (RAG) is an approach that enhances language models by combining them with information retrieval systems.", "RAG allows models to access external knowledge bases and documents, providing more accurate and contextually relevant responses than standalone generation."], "difficulty": "intermediate", "category": "definition"}, {"id": "test_007", "question": "How do you handle missing values in a pandas DataFrame?", "ground_truth_answer": "Missing values in pandas can be handled using methods like dropna() to remove rows/columns with missing data, fillna() to replace missing values with specific values or statistics, or interpolate() for time series data.", "source_document": "python_dev_guide.txt", "expected_contexts": ["Pandas provides several methods for handling missing data: dropna() removes rows or columns containing NaN values, while fillna() replaces missing values with specified values.", "For more sophisticated approaches, you can use fillna() with statistical measures like mean or median, or interpolate() for time-based data."], "difficulty": "intermediate", "category": "factual"}, {"id": "test_008", "question": "What is the capital of Mars?", "ground_truth_answer": "This question cannot be answered from the provided documents as Mars does not have a capital city.", "source_document": null, "expected_contexts": [], "difficulty": "edge_case", "category": "unanswerable"}, {"id": "test_009", "question": "Compare vector databases and traditional relational databases for RAG applications.", "ground_truth_answer": "Vector databases are optimized for similarity search and high-dimensional embeddings, making them ideal for RAG applications, while traditional relational databases excel at structured data and ACID transactions but are less suitable for semantic search.", "source_document": "nlp_rag_concepts.txt", "expected_contexts": ["Vector databases are specifically designed to store and query high-dimensional embeddings efficiently, using specialized indexing techniques like HNSW or IVF.", "Traditional relational databases excel at structured queries and ACID compliance but struggle with similarity search operations required for semantic retrieval in RAG systems."], "difficulty": "advanced", "category": "comparison"}, {"id": "test_010", "question": "What are the best practices for Python virtual environment management?", "ground_truth_answer": "Best practices include creating isolated environments for each project, using requirements.txt or poetry for dependency management, activating environments before installing packages, and keeping environments lightweight and project-specific.", "source_document": "python_dev_guide.txt", "expected_contexts": ["Always create separate virtual environments for different projects to avoid dependency conflicts and maintain clean development environments.", "Use tools like venv, conda, or poetry to manage environments, and maintain requirements.txt files to ensure reproducible installations across different systems."], "difficulty": "basic", "category": "enumeration"}, {"id": "test_011", "question": "How do RAG systems and traditional search engines differ in their approach to information retrieval?", "source_document": ["nlp_rag_concepts.txt", "ai_ml_overview.md"], "ground_truth_answer": "RAG systems use semantic similarity and embeddings to retrieve contextually relevant information for generation, while traditional search engines rely primarily on keyword matching and ranking algorithms to return relevant documents or web pages.", "expected_contexts": ["RAG systems leverage dense vector representations and semantic similarity to find contextually relevant information that supports text generation.", "Traditional search engines use keyword-based indexing, TF-IDF scoring, and PageRank-style algorithms to rank and return relevant documents.", "The key difference is that RAG focuses on retrieving information to augment generation, while search engines aim to return the most relevant documents directly to users."], "difficulty": "advanced", "category": "multi_document"}, {"id": "test_012", "question": "machine learning python", "ground_truth_answer": "This query is too ambiguous to provide a specific answer. Please provide a more specific question about machine learning in Python, such as asking about specific libraries, techniques, or implementation details.", "source_document": null, "expected_contexts": [], "difficulty": "edge_case", "category": "ambiguous"}]}